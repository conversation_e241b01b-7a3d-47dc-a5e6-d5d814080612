import { Button } from "antd";
import { useState } from "react";
import AddMemberModal, { MemberFormData } from "../modals/setting/addMemberModal";

export default function AddMemberModalDemo() {
  const [modalOpen, setModalOpen] = useState(false);

  const handleAddMember = (values: MemberFormData) => {
    console.log("添加成员:", values);
    setModalOpen(false);
    // 这里可以调用API添加成员
  };

  return (
    <div className="p-8">
      <h1 className="mb-4 text-2xl font-bold">添加成员弹窗演示</h1>
      <Button type="primary" onClick={() => setModalOpen(true)}>
        打开添加成员弹窗
      </Button>
      
      <AddMemberModal
        open={modalOpen}
        onCancel={() => setModalOpen(false)}
        onOk={handleAddMember}
      />
    </div>
  );
}
