import { SearchOutlined } from "@ant-design/icons";
import {
  Button,
  Divider,
  Input,
  Pagination,
  Space,
  Table,
  TableProps,
} from "antd";
import { useState } from "react";

import { Badge, BadgeVariant } from "@/components/main/badge";
import { useModal } from "@/hooks/useModal";
import DeleteConfirmModal from "@/modals/deleteConfirmModal";
import AddMemberModal, {
  MemberFormData,
} from "@/modals/setting/addMemberModal";
import MemberPasswordModal from "@/modals/setting/memberPasswordModal";
import type { MemberListItem } from "@/types/member";

function MemberTable() {
  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [searchValue, setSearchValue] = useState("");

  const addMemberModal = useModal<MemberListItem>();
  const memberPasswordModal = useModal();
  const deleteModal = useModal<string>();

  // 模拟数据，根据设计稿内容
  const data: MemberListItem[] = Array.from({ length: 18 }, (_, index) => ({
    key: `${index + 1}`,
    name: `用户${index + 1}`,
    mail: `user${index + 1}@dtstack.com`,
    department: "技术研发部",
    role: index === 0 ? "所有者" : index === 1 ? "管理员" : "成员",
    status: index % 3 === 1 ? 0 : 1,
    joinTime: "2025-08-01 10:00:00",
  }));

  const columns: TableProps<MemberListItem>["columns"] = [
    {
      title: "姓名",
      dataIndex: "name",
      key: "name",
      fixed: "left",
      width: 120,
    },
    {
      title: "邮箱",
      dataIndex: "mail",
      key: "mail",
      width: 200,
    },
    {
      title: "部门",
      dataIndex: "department",
      key: "department",
      width: 120,
    },
    {
      title: "角色",
      key: "role",
      dataIndex: "role",
      width: 100,
      render: (text) => <Badge>{text}</Badge>,
    },
    {
      title: "状态",
      key: "status",
      dataIndex: "status",
      width: 100,
      render: (status: 0 | 1) => (
        <Badge
          variant={status === 1 ? BadgeVariant.SUCCESS : BadgeVariant.WARN}
          showDot={true}
        >
          {status === 1 ? "有效" : "待激活"}
        </Badge>
      ),
    },
    {
      title: "加入时间",
      key: "joinTime",
      dataIndex: "joinTime",
      width: 180,
    },
    {
      title: "操作",
      key: "action",
      width: 150,
      fixed: "right",
      render: (_, record) => (
        <Space size={0} split={<Divider type="vertical" />}>
          <Button
            type="link"
            size="small"
            className="p-0"
            onClick={() => addMemberModal.open(record)}
          >
            编辑
          </Button>
          <Button
            type="link"
            size="small"
            className="p-0"
            onClick={() => memberPasswordModal.open()}
          >
            改密
          </Button>
          <Button
            type="link"
            size="small"
            className="p-0"
            onClick={() => deleteModal.open(record.name)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  const handleSearch = (value: string) => {
    setSearchValue(value);
    // 这里可以添加搜索逻辑
  };

  const handleAddMember = () => {
    addMemberModal.open();
  };

  const handleAddMemberSubmit = (values: MemberFormData) => {
    console.log("添加成员:", values);
    addMemberModal.close();
    // 这里可以调用API添加成员
  };

  const handlePageChange = (page: number, size?: number) => {
    setCurrent(page);
    if (size) {
      setPageSize(size);
    }
  };

  return (
    <div className="flex h-full w-full flex-col">
      {/* 表格头部 */}
      <div className="mb-2 flex h-6 items-center justify-between">
        <span className="text-base font-medium">AIWorks 团队</span>
        <div style={{ display: "flex", gap: "12px", alignItems: "center" }}>
          <Input
            className="w-[240px]"
            placeholder="搜索提示..."
            prefix={<SearchOutlined style={{ color: "#bfbfbf" }} />}
            value={searchValue}
            onChange={(e) => handleSearch(e.target.value)}
            allowClear
            size="small"
          />
          <Button onClick={handleAddMember} size="small" className="text-xs">
            添加成员
          </Button>
        </div>
      </div>

      {/* 表格 */}
      <div className="flex min-h-0 flex-1 flex-col overflow-hidden rounded-md">
        <Table<MemberListItem>
          columns={columns}
          dataSource={data.slice((current - 1) * pageSize, current * pageSize)}
          scroll={{ x: 800, y: "calc(100% - 40px)" }}
          size="small"
          style={{
            backgroundColor: "#fff",
          }}
          pagination={false}
          footer={() => (
            <Pagination
              current={current}
              pageSize={pageSize}
              total={data.length}
              onChange={handlePageChange}
              showSizeChanger={false}
              showQuickJumper={false}
              showTotal={(total) => `共 ${total} 条数据，每页 ${pageSize} 条`}
              size="small"
              align="end"
            />
          )}
        />
      </div>
      <AddMemberModal
        data={addMemberModal.data}
        open={addMemberModal.visible}
        onCancel={() => addMemberModal.close()}
        onOk={handleAddMemberSubmit}
      />
      <MemberPasswordModal
        open={memberPasswordModal.visible}
        onOk={() => memberPasswordModal.close()}
        onCancel={() => memberPasswordModal.close()}
      />
      <DeleteConfirmModal
        title="确认删除该成员吗？"
        description={`即将删除${deleteModal.data}，该操作不可逆，请谨慎操作！`}
        open={deleteModal.visible}
        setOpen={(open) => deleteModal.toggle(open)}
        onConfirm={() => {
          // TODO: 删除成员
          deleteModal.close();
        }}
      />
    </div>
  );
}

export default MemberTable;
